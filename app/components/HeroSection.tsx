'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Shield, Clock, DollarSign } from 'lucide-react';
import AddressAutocomplete from './AddressAutocomplete';
import { AutoCompleteResult } from '@/app/types';

interface HeroSectionProps {
  onAddressSelect: (address: AutoCompleteResult) => void;
}

export default function HeroSection({ onAddressSelect }: HeroSectionProps) {
  const features = [
    {
      icon: FileText,
      title: 'Professional Forms',
      description: 'Access state-specific forms and documents',
    },
    {
      icon: Shield,
      title: 'Legally Compliant',
      description: 'Ensure your offers meet legal requirements',
    },
    {
      icon: Clock,
      title: 'Save Time',
      description: 'Generate agreements in minutes, not hours',
    },
    {
      icon: DollarSign,
      title: 'Affordable',
      description: 'Professional agreements without agent fees',
    },
  ];

  return (
    <div className="h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative safe-area-padding">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -inset-10 opacity-30 md:opacity-50">
          <div className="absolute top-1/4 left-1/4 w-48 h-48 md:w-72 md:h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
          <div className="absolute top-1/3 right-1/4 w-48 h-48 md:w-72 md:h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-48 h-48 md:w-72 md:h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 h-full flex flex-col justify-between">
        {/* Header */}
        <div className="flex-shrink-0 pt-6 md:pt-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <div className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white text-xs md:text-sm font-medium">
                <span className="w-1.5 h-1.5 md:w-2 md:h-2 bg-green-400 rounded-full mr-1.5 md:mr-2 animate-pulse"></span>
                <span className="hidden sm:inline">Professional Real Estate Documents</span>
                <span className="sm:hidden">Real Estate Documents</span>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-8">
          <div className="w-full max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="space-y-6 md:space-y-8"
            >
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                <span className="block">Professional Purchase</span>
                <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Agreements
                </span>
                <span className="block text-2xl sm:text-3xl md:text-4xl lg:text-4xl text-gray-300 font-normal mt-2">
                  Without an Agent
                </span>
              </h1>

              <p className="text-base sm:text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Wholesalers, Section 8 investors, and buyers can now create legally compliant
                as-is purchase agreements in minutes.
              </p>

              {/* Address input section */}
              <div className="w-full max-w-2xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="bg-white/10 backdrop-blur-md p-6 md:p-8 rounded-2xl border border-white/20 shadow-2xl"
                >
                  <h2 className="text-xl md:text-2xl font-semibold text-white mb-4">
                    Get Started Now
                  </h2>
                  <p className="text-sm md:text-base text-gray-300 mb-6">
                    Enter a property address or paste a listing URL to begin
                  </p>
                  <div className="relative">
                    <AddressAutocomplete onSelect={onAddressSelect} />
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Features section - bottom of screen */}
        <div className="flex-shrink-0 pb-6 md:pb-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            {/* Features grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-6"
            >
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm p-4 md:p-5 rounded-xl border border-white/10 text-center hover:bg-white/10 transition-all duration-300"
                >
                  <feature.icon className="w-8 h-8 md:w-10 md:h-10 text-blue-400 mb-3 mx-auto" />
                  <h3 className="text-sm md:text-base font-semibold text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-xs md:text-sm text-gray-300 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </motion.div>

            {/* Pricing preview */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              className="text-center"
            >
              <div className="inline-flex items-center space-x-6 bg-white/5 backdrop-blur-sm px-6 py-3 rounded-full border border-white/10">
                <div className="text-center">
                  <div className="text-lg font-bold text-white">Free</div>
                  <p className="text-xs text-gray-300">Initial</p>
                </div>
                <div className="w-px h-6 bg-white/20"></div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-400">$5</div>
                  <p className="text-xs text-gray-300">Complete</p>
                </div>
                <div className="w-px h-6 bg-white/20"></div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-400">$15/mo</div>
                  <p className="text-xs text-gray-300">Unlimited</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}