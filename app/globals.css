@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-neutral-200;
  }
  body {
    @apply bg-white text-gray-900;
  }

  /* Prevent scrolling on homepage */
  .homepage-container {
    height: 100vh;
    overflow: hidden;
  }
}

@layer utilities {
  .step-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Custom animation delays */
  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Gradient text animation */
  .gradient-text {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Backdrop blur fallback */
  .backdrop-blur-fallback {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* Smooth transitions for all interactive elements */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
